<template>
  <fragment>
    <!-- 页面标题和说明 -->
    <div class="com_title">
      <h5>
        感恩与您携手的美好时光，衷心希望您能告诉我们销户辞别的原因，让未来更好的国金与您再相逢。
      </h5>
    </div>

    <!-- 销户原因选择区域 -->
    <div class="cm_sele_wrap">
      <h5 class="title">请选择您转销户的原因(支持多选)∶</h5>

      <!-- 原因列表 -->
      <ul class="cm_sele_list reason-list">
        <fragment
          v-for="(parentReason, parentIndex) in cancelReasonList"
          :key="parentIndex"
        >
          <!-- 父级原因项 -->
          <li @click="handleReasonSelection(parentReason)">
            <div class="layout">
              <span
                class="icon_check"
                :class="{ checked: parentReason.isChecked }"
              ></span>
              <p>{{ parentReason.dictLabel }}</p>
            </div>
          </li>

          <!-- 子级原因项 -->
          <li
            v-for="(childReason, childIndex) in parentReason.children"
            :key="`${parentIndex}-${childIndex}`"
            class="sub_item sub-reason-item"
            @click="handleReasonSelection(childReason)"
          >
            <div class="layout">
              <span
                class="icon_check"
                :class="{ checked: childReason.isChecked }"
              ></span>
              <p>{{ childReason.dictLabel }}</p>
            </div>
          </li>
        </fragment>
      </ul>

      <!-- 其他原因输入框 -->
      <div class="notes_input" v-if="shouldShowOtherInput">
        <textarea
          placeholder="请输入"
          maxlength="100"
          v-model="otherReasonText"
        ></textarea>
      </div>
    </div>
  </fragment>
</template>

<script>
import { queryDictProps } from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'CancelAccountReasonV4',
  inject: ['eventMessage'],
  data() {
    return {
      cancelReasonList: [], // 销户原因列表
      otherReasonText: '' // 其他原因文本内容
    };
  },
  watch: {
    // 监听其他原因文本输入，过滤emoji表情
    otherReasonText: {
      handler(newValue) {
        // emoji表情正则表达式
        const emojiRegex =
          /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[A9|AE]\u3030|\uA9|\uAE|\u3030/gi;

        if (emojiRegex.test(newValue)) {
          this.otherReasonText = newValue.replace(emojiRegex, '');
          console.log('过滤emoji表情:', newValue);
        }
      }
    },

    // 监听下一步按钮状态变化
    isNextButtonEnabled: {
      handler(isEnabled) {
        if (isEnabled) {
          const formData = {
            reason_acc_cancel: this.selectedReasonsText,
            reason_acc_cancel_other: this.shouldShowOtherInput
              ? this.otherReasonText
              : ''
          };
          this.$emit('change', formData);
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 1
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 0
          });
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 计算下一步按钮是否可用
    isNextButtonEnabled() {
      const hasSelectedReason = this.cancelReasonList.some((parentReason) => {
        if (parentReason.isChecked) {
          // 如果父级原因有子项，需要至少选择一个子项
          if (parentReason.children && parentReason.children.length > 0) {
            return parentReason.children.some(
              (childReason) => childReason.isChecked
            );
          }
          return true;
        }
        return false;
      });

      // 如果显示其他原因输入框，则需要填写内容
      return (
        hasSelectedReason &&
        (!this.shouldShowOtherInput || this.otherReasonText !== '')
      );
    },

    // 获取已选择的原因文本
    selectedReasonsText() {
      const selectedReasons = [];

      this.cancelReasonList.forEach((parentReason) => {
        if (parentReason.isChecked) {
          if (parentReason.children && parentReason.children.length > 0) {
            // 有子项的情况，收集选中的子项
            parentReason.children.forEach((childReason) => {
              if (childReason.isChecked) {
                selectedReasons.push(
                  `${parentReason.dictLabel},${childReason.dictLabel}`
                );
              }
            });
          } else {
            // 没有子项的情况，直接添加父项
            selectedReasons.push(parentReason.dictLabel);
          }
        }
      });

      return selectedReasons.join(';');
    },

    // 判断是否应该显示其他原因输入框
    shouldShowOtherInput() {
      const otherReasonItem = this.cancelReasonList.find(
        (reason) => reason.dictValue === '99'
      );
      return otherReasonItem ? otherReasonItem.isChecked : false;
    }
  },
  created() {
    // 设置页面背景为白色
    this.$store.commit('flow/setWhiteBg', true);
  },

  mounted() {
    this.loadCancelReasons();
  },
  methods: {
    // 加载销户原因数据
    async loadCancelReasons() {
      try {
        const reasonData = await queryDictProps('bc.common.closeAccReason');
        this.cancelReasonList = this.buildReasonHierarchy(reasonData);
      } catch (error) {
        this.$TAlert({
          tips: error
        });
      }
    },

    // 构建原因层级结构
    buildReasonHierarchy(reasonList) {
      // 筛选出父级原因（dictValue长度为2）
      const parentReasons = reasonList
        .filter((reason) => reason.dictValue.length === 2)
        .map((reason) => ({
          ...reason,
          children: [],
          isChecked: false
        }));

      // 筛选出子级原因（dictValue长度为4）
      const childReasons = reasonList.filter(
        (reason) => reason.dictValue.length === 4
      );

      // 创建父级原因映射表，便于快速查找
      const parentReasonMap = parentReasons.reduce((map, parentReason) => {
        map[parentReason.dictValue] = parentReason;
        return map;
      }, {});

      // 将子级原因分配到对应的父级原因下
      childReasons.forEach((childReason) => {
        const parentValue = childReason.dictValue.substring(0, 2);
        const parentReason = parentReasonMap[parentValue];

        if (parentReason) {
          parentReason.children.push({
            ...childReason,
            isChecked: false
          });
        }
      });

      return parentReasons;
    },
    // 处理原因选择前的预检查（可能包含警告信息）
    handleReasonSelection(reasonItem) {
      const warningMessage = this.extractWarningMessage(reasonItem.dictLabel);

      if (warningMessage) {
        this.showWarningDialog(warningMessage, reasonItem);
      } else {
        this.toggleReasonSelection(reasonItem);
      }
    },

    // 提取警告信息
    extractWarningMessage(dictLabel) {
      const parts = dictLabel.split('|');
      return parts.length > 1 ? parts[1] : null;
    },

    // 显示警告对话框
    showWarningDialog(warningMessage, reasonItem) {
      this.$TAlert({
        title: '温馨提示',
        tips: warningMessage,
        hasCancel: true,
        confirmBtn: '取消销户',
        cancelBtn: '继续销户',
        confirm: () => this.toggleReasonSelection(reasonItem)
      });
    },

    // 切换原因选择状态
    toggleReasonSelection(reasonItem) {
      this.$set(reasonItem, 'isChecked', !reasonItem.isChecked);

      if (this.isParentReason(reasonItem)) {
        this.handleParentReasonToggle(reasonItem);
      } else if (this.isChildReason(reasonItem)) {
        this.handleChildReasonToggle(reasonItem);
      }
    },

    // 判断是否为父级原因
    isParentReason(reasonItem) {
      return reasonItem.children && reasonItem.children.length > 0;
    },

    // 判断是否为子级原因
    isChildReason(reasonItem) {
      return reasonItem.dictValue.length === 4;
    },

    // 处理父级原因切换
    handleParentReasonToggle(parentReason) {
      // 如果父级原因被取消选中，则其所有子项也应被取消选中
      if (!parentReason.isChecked) {
        parentReason.children.forEach((childReason) => {
          this.$set(childReason, 'isChecked', false);
        });
      }
    },

    // 处理子级原因切换
    handleChildReasonToggle(childReason) {
      const parentValue = childReason.dictValue.substring(0, 2);
      const parentReason = this.cancelReasonList.find(
        (reason) => reason.dictValue === parentValue
      );

      if (parentReason) {
        // 如果有任何一个子项被选中，则父项也应被选中
        const hasSelectedChild = parentReason.children.some(
          (child) => child.isChecked
        );
        this.$set(parentReason, 'isChecked', hasSelectedChild);
      }
    }
  }
};
</script>
<style scoped>
/* 父级原因项样式 */
.reason-list li:not(.sub-reason-item) {
  padding: 0.15rem 0;
}

/* 子级原因项样式 */
.sub-reason-item {
  padding: 0.1rem 0 0.1rem 0.2rem;
}

/* 页面编号样式 */
h5.title > .page_num {
  width: 0.35rem;
  height: 0.22rem;
  display: inline-flex;
  background-color: #fa443a;
  color: #ffffff;
  font-size: 0.14rem;
  padding-left: 0.05rem;
  margin-right: 0.1rem;
}
</style>
